<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<!--
=======================================================
JetTheme Blogger Template
Name        : JetTheme Core
Version     : 2.9
Designer    : jettheme
URL         : www.jettheme.com
=======================================================
-->
<html
    b:css='false'
    b:defaultwidgetversion='2'
    b:js='true'
    b:layoutsVersion='3'
    b:render='true'
    b:responsive='true'
    b:templateUrl='https://www.jettheme.com'
    b:templateVersion='2.9'
    expr:dir='data:blog.languageDirection'
    expr:lang='data:blog.locale'
    xmlns='http://www.w3.org/1999/xhtml'
    xmlns:b='http://www.google.com/2005/gml/b'
    xmlns:data='http://www.google.com/2005/gml/data'
    xmlns:expr='http://www.google.com/2005/gml/expr'>

    <!-- Essential Blogger attributes for layout mode functionality -->
    <b:attr name='xmlns' value=''/>
    <b:attr name='xmlns:b' value=''/>
    <b:attr name='xmlns:expr' value=''/>
    <b:attr name='xmlns:data' value=''/>

    <head>
        <!-- Include head content with meta tags and scripts -->
        <b:include data='blog' name='JetAll-head-content'/>

        <!-- Main template styles - CRITICAL: Only load when NOT in layout mode -->
        <b:if cond='!data:view.isLayoutMode'>
            <b:skin><![CDATA[/*
=======================================================
JetTheme v2.9 - Blogger Template
=======================================================

IMPORTANT NOTES:
- This template uses Blogger's layout mode functionality
- DO NOT modify or remove data:view.isLayoutMode conditions
- DO NOT modify or remove b:template-skin sections
- Layout mode allows template customization from Blogger interface

TEMPLATE STRUCTURE:
1. Variable Definitions - Customizable theme settings
2. CSS Custom Properties - Modern CSS variables
3. Base Styles - Core typography and layout
4. Component Styles - Buttons, forms, utilities
5. Layout Styles - Header, navigation, footer
6. Responsive Styles - Mobile and desktop breakpoints
7. Layout Mode Styles - Essential for Blogger editor

CUSTOMIZATION:
- Edit variables in the Variable sections below
- Add custom CSS at the end of this file
- Use the layout mode to arrange widgets

=======================================================

[!] Find (JetAll-head-content) to edit Open Graph and other Meta Tags

---------------------------
# JetTheme v2.9 Settings #
---------------------------

<Variable name="tagline" description="Tagline" type="string" value=""/>
[!] Homepage tagline, ex: BlogName - Tagline

<Variable name="separator" description="Separator" type="string" value=" - "/>
[!] Title separator ( – ), ( | ), ( • )

<Variable name="description" description="Description" type="string" value=""/>
[!] Default meta description, recommended 155–160 characters

<Variable name="cover" description="Cover" type="string" value=""/>
[!] Default meta image, size recommended 1600x700 px

<Variable name="logo" description="Logo" type="string" value=""/>
[!] Schema logo, size recommended 175x55 px

<Variable name="favicon" description="Favicon" type="string" value=""/>
[!] Favicon high resolution, required format (.png), min size 200x200 px

<Variable name="analyticId" description="Analytic ID" type="string" value=""/>
[!] New Google Analytic 2021

<Variable name="caPubAdsense" description="caPubAdsense ID" type="string" value=""/>
[!] number only

<Variable name="autoTOC" description="Table of content" type="string" value="true"/>
[!] true or false

<Variable name="positionTOC" description="Position TOC" type="string" value="noscript"/>
[!] tag name, class, id

<Variable name="maxLabel" description="Limit Label" type="string" value="3"/>
[!] Limit Label Display

---------------------------
# JetTheme CSS Variables #
---------------------------

<!-- Main Color Variables -->
<Group description="_Main Color">
    <Variable name="keycolor" description="Main Color" type="color" default="#f67938" value="#f67938"/>
    <Variable name="body.background" description="Body Background Color" type="color" default="#ffffff" value="#ffffff"/>
    <Variable name="body.text.color" description="Font Color" type="color" default="#686868" value="#686868"/>
    <Variable name="posts.title.color" description="Heading Color" type="color" default="#343a40" value="#000000"/>
    <Variable name="body.link.color" description="Link Color" type="color" default="$(keycolor)" value="#f67938"/>
    <Variable name="body.hover.color" description="Link Hover" type="color" default="#f46013" value="#f46013"/>
    <Variable name="border.color" description="Border Color" type="color" default="#efefef" value="#efefef"/>
    <Variable name="posts.text.color" description="Blockquote Border Color" type="color" default="$(keycolor)" value="#f67938"/>
</Group>

<!-- Typography Variables -->
<Group description="_Typography">
    <Variable name="body.text" description="Body Font" type="font" default="normal normal 16px system-ui,-apple-system,Segoe UI,Helvetica Neue,Arial,Noto Sans,Liberation Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji" value="normal normal 16px system-ui,-apple-system,Segoe UI,Helvetica Neue,Arial,Noto Sans,Liberation Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"/>
    <Variable name="posts.title" description="Heading Font" type="font" default="normal bold 40px var(--bs-font-sans-serif)" value="normal bold 40px var(--bs-font-sans-serif)"/>
    <Variable name="title.h2" description="H2 Size" type="length" default="26px" min="12px" max="50px" value="26px"/>
    <Variable name="title.h3" description="H3 Size" type="length" default="22px" min="12px" max="50px" value="22px"/>
    <Variable name="title.h4" description="H4 Size" type="length" default="20px" min="12px" max="50px" value="20px"/>
    <Variable name="title.h5" description="H5 Size" type="length" default="18px" min="12px" max="50px" value="18px"/>
    <Variable name="title.h6" description="H6 Size" type="length" default="16px" min="12px" max="50px" value="16px"/>
</Group>

<!-- Logo Variables -->
<Group description="_Logo">
    <Variable name="logo.width" description="Logo Max Width" type="length" default="200px" min="50px" max="500px" value="200px"/>
    <Variable name="logo.width.mobile" description="Logo Max Width (mobile)" type="length" default="150px" min="50px" max="500px" value="150px"/>
</Group>

<!-- Header Color Variables -->
<Group description="_Header Color">
    <Variable name="header.bg" description="Background" type="color" default="#ffffff" value="#ffffff"/>
    <Variable name="header.color" description="Font Color " type="color" default="#800000" value="#800000"/>
    <Variable name="header.border" description="Border Color" type="color" default="#efefef" value="#efefef"/>
</Group>

<!-- Menu Variables -->
<Group description="_Menu">
    <Variable name="tabs.font" description="Menu Font" type="font" default="normal bold 16px var(--bs-font-sans-serif)" value="normal bold 16px var(--bs-font-sans-serif)"/>
    <Variable name="tabs.color" description="Font Color" type="color" default="$(body.text.color)" value="#686868"/>
    <Variable name="tabs.hover" description="Font Hover" type="color" default="$(keycolor)" value="#f67938"/>
    <Variable name="tabs.selected.color" description="Font Selected" type="color" default="$(keycolor)" value="#f67938"/>
</Group>

<!-- Dropdown Menu Variables -->
<Group description="_Dropdown Menu">
    <Variable name="dropdown.font" description="Font Size" type="length" default="16px" min="12px" max="50px" value="15px"/>
    <Variable name="dropdown.bg" description="Background Color" type="color" default="$(body.text.color)" value="#ffffff"/>
    <Variable name="dropdown.color" description="Font Color" type="color" default="$(keycolor)" value="#686868"/>
    <Variable name="dropdown.hover" description="Font Hover" type="color" default="$(keycolor)" value="#f67938"/>
    <Variable name="dropdown.selected" description="Dropdown Selected Color" type="color" default="$(keycolor)" value="#f67938"/>
</Group>

<!-- Footer Color Variables -->
<Group description="_Footer Color">
    <Variable name="footer.bg" description="Background Color" type="color" default="#212529" value="#212529"/>
    <Variable name="footer.color" description="Font Color" type="color" default="#800000" value="#800000"/>
    <Variable name="footer.link" description="Link Color" type="color" default="#800000" value="#800000"/>
    <Variable name="footer.border" description="Border Color" type="color" default="#323539" value="#323539"/>
</Group>

<!-- Socket Color Variables -->
<Group description="_Socket Color">
    <Variable name="socket.bg" description="Background Color" type="color" default="#09080c" value="#09080c"/>
    <Variable name="socket.color" description="Font Color" type="color" default="#9fa6ad" value="#9fa6ad"/>
</Group>

<!-- Button Variables -->
<Group description="_Button">
    <Variable name="posts.icons.color" description="Button Color" type="color" default="$(keycolor)" value="#f67938"/>
    <Variable name="labels.background.color" description="Button Hover"  type="color"  default="#f46013" value="#f46013"/>
</Group>

<!-- Comment Form Variables -->
<Group description="_Comment Form">
    <Variable name="body.text.font" description="Font" type="font" default="normal normal 14px system-ui,-apple-system,Segoe UI,Helvetica Neue,Arial,Noto Sans,Liberation Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji" value="normal normal 14px system-ui,-apple-system,Segoe UI,Helvetica Neue,Arial,Noto Sans,Liberation Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"/>
    <Variable name="posts.background.color" description="Background" type="color" default="transparent" value="rgba(0,0,0,0)"/>
</Group>
*/

/*
=======================================================
JetTheme Blogger Template
Name        : JetTheme Core
Version     : 2.9
Designer    : jettheme
URL         : www.jettheme.com
=======================================================
*/

/* CSS Custom Properties (Variables) */
:root {
    /* Base Bootstrap Variables */
    --bs-font-sans-serif: $(body.text.family);
    --bs-body-bg: $(body.background);
    --bs-body-color: $(body.text.color);

    /* JetTheme Primary Colors */
    --jt-primary: $(keycolor);
    --jt-heading-color: $(posts.title.color);
    --jt-heading-link: $(posts.title.color);
    --jt-heading-hover: $(keycolor);
    --jt-link-color: $(body.link.color);
    --jt-link-hover: $(body.hover.color);
    --jt-blockquote: $(posts.text.color);

    /* Button Colors */
    --jt-btn-primary: $(posts.icons.color);
    --jt-btn-primary-hover: $(labels.background.color);
    --jt-btn-light-hover: $(posts.title.color);

    /* Background & Border Colors */
    --jt-border-light: $(border.color);
    --jt-bg-light: #f3f7f9;
    --jt-archive-bg: #ffffff;

    /* Navigation Colors */
    --jt-nav-color: $(tabs.color);
    --jt-nav-hover: $(tabs.hover);
    --jt-nav-selected: $(tabs.selected.color);

    /* Dropdown Colors */
    --jt-dropdown-bg: $(dropdown.bg);
    --jt-dropdown-color: $(dropdown.color);
    --jt-dropdown-hover: $(dropdown.hover);
    --jt-dropdown-selected: $(dropdown.selected);

    /* Header Colors */
    --jt-header-bg: $(header.bg);
    --jt-header-color: $(header.color);
    --jt-header-border: $(header.border);

    /* Footer Colors */
    --jt-footer-bg: $(footer.bg);
    --jt-footer-color: $(footer.color);
    --jt-footer-link: $(footer.link);
    --jt-footer-border: $(footer.border);

    /* Socket Colors */
    --jt-socket-bg: $(socket.bg);
    --jt-socket-color: $(socket.color);
}
